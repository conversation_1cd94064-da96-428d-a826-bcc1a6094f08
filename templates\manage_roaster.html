{% extends "base.html" %}
{% block title %}ניהול בית קלייה{% endblock %}
{% block content %}
<style>
    body {font-family: sans-serif; margin: 20px; direction: rtl; text-align: right;}
    form label { display: block; margin-bottom: 5px; }
    form select { margin-bottom: 10px; padding: 5px; direction: rtl;}
    table { width: 100%; border-collapse: collapse; margin-top: 20px; }
    table, th, td { border: 1px solid black; }
    th, td { padding: 10px; text-align: right; }

    /* Button styles consistent with search_beans.html */
    .action-button {
        padding: 4px 8px;
        border-radius: 4px;
        cursor: pointer;
        font-family: 'Varela Round', sans-serif;
        display: inline-block;
        text-align: center;
        min-width: 40px;
        margin: 1px;
        font-size: 12px;
    }

    /* Colors only when not in high-contrast mode */
    body:not(.high-contrast) .action-button {
        background-color: #4CAF50;
        color: white;
        border: none;
    }

    body:not(.high-contrast) .action-button:hover {
        background-color: #45a049;
    }

    /* Special style for delete button */
    body:not(.high-contrast) .delete-button {
        background-color: #e31111;
    }

    body:not(.high-contrast) .delete-button:hover {
        background-color: #c62828;
    }

    /* High contrast mode styles */
    body.high-contrast .action-button,
    body.high-contrast .delete-button {
        background-color: black !important;
        color: white !important;
        border: 1px solid white !important;
    }

    /* Light background mode */
    body.light-background .action-button {
        background-color: #4CAF50 !important;
        color: white !important;
    }

    body.light-background .delete-button {
        background-color: #e31111 !important;
        color: white !important;
    }

    /* Enhanced styles for btn-primary and btn-success */
    .btn-primary, .btn-success {
        background-color: #2e56f4 !important; /* Light blue */
        color: #fff !important;
        border: 2px solid #1a3dc9 !important; /* Darker border for depth */
        border-radius: 6px !important; /* Rounded corners */
        padding: 8px 16px !important; /* More padding for better clickable area */
        font-weight: bold !important; /* Bold text */
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important; /* Subtle shadow for depth */
        transition: all 0.2s ease !important; /* Smooth transition for hover effects */
        margin: 5px !important; /* Add some margin around buttons */
        display: inline-block !important; /* Ensure proper display */
        text-align: center !important; /* Center text */
        cursor: pointer !important; /* Show pointer cursor on hover */
    }
    .btn-primary:hover, .btn-success:hover {
        background-color: #1a3dc9 !important; /* Darker blue on hover */
        color: #fff !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important; /* Enhanced shadow on hover */
        transform: translateY(-2px) !important; /* Slight lift effect */
    }
    
    /* Different color for btn-success */
    .btn-success {
        background-color: #28a745 !important; /* Green */
        border-color: #1e7e34 !important; /* Darker green border */
    }
    
    .btn-success:hover {
        background-color: #218838 !important; /* Darker green on hover */
    }
</style>
<div class="container" dir="rtl">
    <h1>ניהול בית קלייה: <span style="color: blue;">{{ roaster.name }}</span></h1>
    
    <div class="roaster-details">
        <h2>פרטי בית קלייה</h2>
        <div class="card">
            <div class="card-body">
                <p><strong>שם:</strong> {{ roaster.name }}</p>
                <p><strong>כתובת:</strong> {{ roaster.address }}</p>
                <p><strong>עיר:</strong> {{ roaster.city }}</p>
                <p><strong>מיקוד:</strong> {{ roaster.zip }}</p>
                <p><strong>דוא"ל:</strong> {{ roaster.email }}</p>
                <p><strong>אתר:</strong> {{ roaster.webpage }}</p>
                <p><strong>משלוח חינם מעל:</strong> {{ roaster.minimun_shipping }}</p>
                <p><strong>דמי משלוח:</strong> {{ roaster.shipping_cost }}</p>
                <a href="{{ url_for('edit_roaster') }}" class="btn btn-primary">ערוך פרטים</a>
            </div>
        </div>
    </div>
    
    <div class="beans-management mt-4">
        <h2>ניהול פולי קפה</h2>
        <a href="{{ url_for('add_bean') }}" class="btn btn-success mb-3">הוסף פולים חדשים</a>
        
        {% if beans %}
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>שם פולי קפה</th>
                    <th>מקור</th>
                    <th>עיבוד</th>
                    <th>טעמים</th>
                    <th>דרגת קלייה</th>
                    <th>ערביקה</th>
                    <th>רובוסטה</th>
                    <th>תערובת</th>
                    <th>מחיר</th>
                    <th>משקל</th>
                    <th>תמונה</th>
                    <th>פעולות</th>
                </tr>
            </thead>
            <tbody>
                {% for bean in beans %}
                <tr>
                    <td>{{ bean.bean_name }}</td>
                    <td>{{ bean.origin }}</td>
                    <td>{{ bean.processing }}</td>
                    <td>{{ bean.flavors }}</td>
                    <td>{{ bean.roast_level }}</td>
                    <td>{{ bean.arabica }}</td>
                    <td>{{ bean.robusta }}</td>
                    <td>{{ bean.mix }}</td>
                    <td>{{ bean.price }}</td>
                    <td>{{ bean.weight }}</td>
                    <td><img src="{{ bean.image_file }}" alt="Image" style="width: 50px; height: 50px;"></td>
                    <td>
                        <form method="post" action="{{ url_for('edit_bean') }}" style="display:inline;">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <input type="hidden" name="bean_id" value="{{ bean.bean_id }}">
                            <button type="submit" class="action-button">ערוך</button>
                        </form>
                        <form method="post" action="{{ url_for('delete_bean') }}" style="display:inline;">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <input type="hidden" name="bean_id" value="{{ bean.bean_id }}">
                            <button type="submit" class="action-button delete-button" onclick="return confirm('האם אתה בטוח שברצונך למחוק את {{ bean.bean_name }}?')">מחק</button>
                        </form>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% else %}
        <p>לא נמצאו פולי קפה עבור בית הקלייה זה.</p>
        {% endif %}
    </div>
</div>
{% endblock %}
